import os
import json
import xml.etree.ElementTree as ET
from openai import OpenAI
from typing import List, Dict
import fnmatch

# ====== 配置区域 (用户可根据需要修改这些参数) ======
PROJECT_ROOT = "/Users/<USER>/IdeaProjects/ta3404-back-components"  # Maven项目根目录
OUTPUT_FILE = "project_structure.json"  # 输出JSON文件名
README_FILES = ['README.md', 'README.txt', 'README']  # 支持的README文件名
MAX_README_CHARS = 2000  # 最多读取的README字符数

# 初始化OpenAI客户端
client = OpenAI(
    api_key="********************************************************************************************************************************************************************")


# ====== 配置结束 ======

def parse_pom(pom_path):
    """解析pom.xml文件，提取模块信息"""
    modules = []
    try:
        tree = ET.parse(pom_path)
        root = tree.getroot()

        # 处理Maven的XML命名空间
        ns = {'maven': 'http://maven.apache.org/POM/4.0.0'}
        modules_elem = root.find('maven:modules', ns) or root.find('modules')

        if modules_elem:
            for module_elem in modules_elem.findall('maven:module', ns) or modules_elem.findall('module'):
                module_name = module_elem.text.strip()
                if module_name:
                    modules.append(module_name)
    except Exception as e:
        print(f"警告: 解析 {pom_path} 失败: {str(e)}")
    return modules


def extract_readme_info(module_path):
    """从README文件中提取模块描述信息"""
    for file in README_FILES:
        readme_path = os.path.join(module_path, file)
        if os.path.exists(readme_path):
            try:
                with open(readme_path, 'r', encoding='utf-8') as f:
                    return f.read(MAX_README_CHARS)
            except Exception as e:
                print(f"警告: 读取 {readme_path} 失败: {str(e)}")
                return f"README读取错误: {str(e)}"
    return "未找到README文件"


def build_module_tree(root_dir, current_path=""):
    """递归构建模块树结构"""
    current_dir = os.path.join(root_dir, current_path) if current_path else root_dir
    pom_path = os.path.join(current_dir, 'pom.xml')

    if not os.path.exists(pom_path):
        return None

    module_info = {
        "path": current_path if current_path else "ROOT",
        "pom_location": pom_path.replace(root_dir, "").lstrip(os.sep),
        "description": extract_readme_info(current_dir),
        "submodules": []
    }

    # 获取并处理子模块
    for module in parse_pom(pom_path):
        module_rel_path = os.path.join(current_path, module) if current_path else module
        submodule = build_module_tree(root_dir, module_rel_path)
        if submodule:
            module_info["submodules"].append(submodule)

    return module_info


def save_to_json(data, output_file):
    """将模块结构保存为JSON文件"""
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump({"modules": data}, f, ensure_ascii=False, indent=2)
    print(f"项目结构已保存至: {output_file}")


def analyze_maven_project():
    """分析Maven项目并生成结构报告"""
    # 验证项目根目录
    root_pom = os.path.join(PROJECT_ROOT, 'pom.xml')
    if not os.path.exists(root_pom):
        print(f"错误: 在 {PROJECT_ROOT} 中未找到pom.xml")
        return False

    print(f"开始分析Maven项目: {PROJECT_ROOT}")
    project_structure = build_module_tree(PROJECT_ROOT)

    if project_structure:
        save_to_json(project_structure, OUTPUT_FILE)


def flatten_modules(module: Dict, module_list: List[Dict] = None) -> List[Dict]:
    """递归展平模块树，仅收集叶子模块（无子模块）的path和description"""
    if module_list is None:
        module_list = []

    # 检查 module 是否为字典
    if not isinstance(module, dict):
        print(f"错误: 模块数据不是字典，而是 {type(module)}: {module}")
        return module_list

    # 检查是否包含 path 字段
    if "path" not in module:
        print(f"警告: 模块缺少 'path' 字段，跳过此模块: {module}")
        return module_list

    # 仅当 submodules 为空时添加模块（叶子模块）
    if not module.get("submodules", []):
        module_list.append({
            "name": module["path"],
            "description": module.get("description", "无描述")
        })

    # 递归处理子模块
    for submodule in module.get("submodules", []):
        flatten_modules(submodule, module_list)

    return module_list


def load_module_info(json_path: str) -> List[Dict]:
    """加载模块信息的JSON文件并展平"""
    if not os.path.exists(json_path):
        raise FileNotFoundError(f"JSON文件不存在: {json_path}")

    with open(json_path, 'r', encoding='utf-8') as f:
        try:
            module_tree = json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON文件格式错误: {e}")

    # 检查 module_tree 是否为字典
    if not isinstance(module_tree, dict):
        raise ValueError(f"JSON根节点必须是字典，实际为 {type(module_tree)}")

    # 提取 modules 下的对象
    if "modules" not in module_tree:
        raise ValueError("JSON文件缺少 'modules' 键")
    root_module = module_tree["modules"]
    if not isinstance(root_module, dict):
        raise ValueError(f"'modules' 键的值必须是字典，实际为 {type(root_module)}")

    return flatten_modules(root_module)


def load_gitignore_patterns(root_dir: str) -> List[str]:
    """加载项目根目录下的 .gitignore 文件，解析忽略模式"""
    gitignore_path = os.path.join(root_dir, ".gitignore")
    patterns = []

    if os.path.exists(gitignore_path):
        try:
            with open(gitignore_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        patterns.append(line)
        except Exception as e:
            print(f"警告: 无法读取 .gitignore 文件 {gitignore_path}: {str(e)}")

    # 明确添加 target/ 到忽略模式
    patterns.append("target/")
    return patterns


def should_ignore(path: str, root_dir: str, patterns: List[str]) -> bool:
    """检查文件或目录是否应被 .gitignore 或 target/ 忽略"""
    rel_path = os.path.relpath(path, root_dir).replace(os.sep, '/')
    for pattern in patterns:
        if fnmatch.fnmatch(rel_path, pattern) or fnmatch.fnmatch(os.path.basename(path), pattern):
            return True
        # 额外检查是否在 target/ 目录下
        if rel_path.startswith("target/") or os.path.basename(path) == "target":
            return True
    return False


def get_directory_tree(module_path: str, root_dir: str) -> str:
    """获取模块的文件列表，排除 .gitignore 和 target/ 忽略的文件，仅列出文件"""
    module_dir = os.path.join(root_dir, module_path.replace('\\', os.sep))
    if not os.path.exists(module_dir):
        print(f"警告: 模块目录不存在: {module_dir}")
        return ""

    # 加载 .gitignore 模式
    gitignore_patterns = load_gitignore_patterns(root_dir)

    # 允许的文件扩展名（可根据需要调整）
    allowed_extensions = {'.java', '.yml', '.yaml', '.xml', '.properties', '.json'}

    def collect_files(directory: str) -> List[str]:
        """递归收集所有文件，排除忽略文件"""
        files = []
        try:
            for entry in sorted(os.listdir(directory)):
                path = os.path.join(directory, entry)
                if should_ignore(path, root_dir, gitignore_patterns):
                    continue
                if os.path.isfile(path) and os.path.splitext(entry)[1].lower() in allowed_extensions:
                    rel_path = os.path.relpath(path, module_dir).replace(os.sep, '/')
                    files.append(rel_path)
                elif os.path.isdir(path):
                    files.extend(collect_files(path))
        except Exception as e:
            print(f"警告: 无法访问 {entry}: {str(e)}")
        return files

    files = collect_files(module_dir)
    return "\n".join(files) if files else ""


def recommend_modules(requirement: str, json_path: str) -> Dict:
    """根据需求推荐最多三个叶子模块"""
    # 加载并展平模块信息（仅叶子模块）
    modules = load_module_info(json_path)

    # 构建模块推荐提示
    prompt = """
You are an expert in software architecture and module analysis. Given a user requirement and a list of leaf modules (modules with no submodules) with their descriptions, determine which module(s) are most relevant for implementing or modifying code to meet the requirement. Return a JSON object with:
- "recommended_modules": a list of up to three leaf module paths that are most relevant
- "reasoning": a brief explanation of why these modules were chosen

**User Requirement:**
{requirement}

**Leaf Modules Information:**
{modules_info}

**Instructions:**
- Analyze the requirement and match it to the module descriptions.
- Recommend up to three most relevant leaf module(s) based on functionality.
- If fewer than three leaf modules are relevant, return only those.
- Ensure all recommended modules are leaf modules (no submodules).
- Return the response in JSON format.
"""
    modules_info = "\n".join([f"- {module['name']}: {module['description']}" for module in modules])
    prompt = prompt.format(requirement=requirement, modules_info=modules_info)

    # 调用OpenAI API推荐模块
    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a helpful assistant for code module analysis."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"}
        )

        content = response.choices[0].message.content
        try:
            result = json.loads(content)
            if len(result.get("recommended_modules", [])) > 3:
                result["recommended_modules"] = result["recommended_modules"][:3]
                result["reasoning"] += " (Note: Limited to top 3 leaf modules as per requirement.)"
            return result
        except json.JSONDecodeError as e:
            raise ValueError(f"OpenAI返回的模块推荐响应不是有效JSON: {content}")
    except Exception as e:
        raise RuntimeError(f"OpenAI API调用失败: {str(e)}")


def analyze_files_with_openai(requirement: str, module_name: str, file_list: str) -> Dict:
    """调用OpenAI分析模块目录树，返回最相关的文件"""
    if not file_list:
        return {"recommended_files": [], "reasoning": f"No files found for module {module_name}"}

    prompt = """
You are a software architecture and file analysis expert. Based on user requirements and the list of files within the module, determine which files are most relevant to implementing or modifying code to meet the requirements. Return a JSON object containing the following:
- "recommended_files": A list of paths to the most relevant files (relative to the module directory)
- "reasoning": A brief explanation of why these files were selected

**User Requirements:**
{requirement}

**Module Name:**
{module_name}

**File List:**
{file_list}

**Instructions:**
- Analyze requirements and match them with filenames and their paths.
- Recommend any number of the most relevant files based on filenames and potential content (e.g., Java files for implementation, configuration files for settings).
- Provide clear and concise reasoning for your choices.
- Return the response in JSON format.

"""
    prompt = prompt.format(requirement=requirement, module_name=module_name, file_list=file_list)

    try:
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a helpful assistant for code file analysis."},
                {"role": "user", "content": prompt}
            ],
            response_format={"type": "json_object"}
        )

        content = response.choices[0].message.content
        try:
            result = json.loads(content)
            return result
        except json.JSONDecodeError as e:
            raise ValueError(f"OpenAI返回的响应不是有效JSON: {content}")
    except Exception as e:
        raise RuntimeError(f"OpenAI API调用失败: {str(e)}")


def recommend_modules_and_files(requirement: str, json_path: str, root_dir: str) -> Dict:
    """先推荐最多三个叶子模块，然后为每个模块推荐相关文件"""
    # 第一步：推荐最多三个叶子模块
    module_result = recommend_modules(requirement, json_path)

    # 第二步：为每个推荐模块分析文件
    result = {
        "recommended_modules": module_result["recommended_modules"],
        "module_reasoning": module_result["reasoning"],
        "recommended_files": []
    }

    for module_path in module_result.get("recommended_modules", []):
        file_list = get_directory_tree(module_path, root_dir)
        print(file_list)
        file_result = analyze_files_with_openai(requirement, module_path, file_list)
        result["recommended_files"].append({
            "module": module_path,
            "files": file_result["recommended_files"],
            "file_reasoning": file_result["reasoning"]
        })

    return result


if __name__ == "__main__":
    # 执行项目分析
    analyze_maven_project()

    json_path = "project_structure.json"
    requirement = "修改框架缓存的默认前缀"

    result = recommend_modules_and_files(requirement, json_path, PROJECT_ROOT)
    print("\n模块推荐结果:")
    print(f"推荐的模块: {result['recommended_modules']}")
    print(f"模块推荐理由: {result['module_reasoning']}")
    print("\n文件推荐结果:")
    for file_result in result["recommended_files"]:
        print(f"\n模块: {file_result['module']}")
        print(f"推荐的文件: {file_result['files']}")
        print(f"文件推荐理由: {file_result['file_reasoning']}")
