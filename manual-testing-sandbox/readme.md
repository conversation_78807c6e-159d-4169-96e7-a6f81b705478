The sole purpose of this folder is to open it when debugging the extension. It is not used by the extension itself. You can add more files that can be useful when manually testing the extension.

# 项目名称

该项目是一个多语言、多功能的技术演示，整合了前端用户界面、后端逻辑处理和各种语言的计算功能。

## 项目结构

- **前端**
  - `AdvancedPage.tsx`: 使用React编写的可切换浅色和深色模式的页面，包含计数器和表单元素。
  - `test.html` 和 `test.css`: 基本的HTML和CSS，用于静态页面的展示。

- **后端逻辑和算法**
  - 多种语言实现的计算器逻辑：
    - `Calculator.java` 和 `program.cs`: 提供加减功能。
    - `test.rs` 和 `test.kt`: 提供加、减、乘、除功能。
  - `Utility.java`: Java编写的实用工具类，提供基础功能如最大值和最小值计算。

- **配置和环境**
  - `Dockerfile`: 用于容器化Python应用的设置。
  - `package.json`: 管理React项目的依赖和脚本。
  - `requirements.txt`: 列出Python项目中的依赖包。

- **数据和测试**
  - `data.json`: 含有公司和员工信息的数据文件。
  - `manual-testing-sandbox.iml`: 用于项目的模块管理。
  - `readme.md`: 项目的初始说明文件。

## 开始使用

### 前端部分

1. 确保安装了Node.js和npm。
2. 进入项目的根目录，运行以下命令启动开发服务器：

   ```bash
   npm install
   npm start
